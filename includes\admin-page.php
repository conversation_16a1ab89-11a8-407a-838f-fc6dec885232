<?php
if (!defined('ABSPATH')) {
    exit;
}

// Include SSL manager functions.
require_once plugin_dir_path( __FILE__ ) . 'ssl-setup.php';

$managerPath = asm_get_binary_dest();
$binDir = dirname($managerPath);
debug_log("Binary directory: $binDir, Manager path: $managerPath");

// These constants are used by the SSL manager functions.
define('API_BASE_URL', 'https://externalautossl.service.spaceship.com/api/v1/externalautossl/user');
define('DOWNLOAD_URL', 'https://ssl-manager.s3.amazonaws.com/bin/ssl-manager');

debug_log("API Base URL: " . API_BASE_URL);
debug_log("Download URL: " . DOWNLOAD_URL);

$errorMessage   = '';
$infoOutput     = '';
$registerOutput = '';
$isPanelError   = false;

// Handle form submissions.
$action = isset($_POST['action']) ? $_POST['action'] : '';
$token  = isset($_POST['token']) ? $_POST['token'] : '';
$panelCredential = isset($_POST['password']) ? $_POST['password'] : '';

if (!empty($action)) {
    debug_log("Action received: " . $action);
}

// 1) If the manager is not installed and the user clicked "install"
if ($action === 'install' && !managerInstalled($managerPath)) {
    debug_log("Install action initiated.");
    if (!is_dir($binDir)) {
        mkdir($binDir, 0755, true);
        debug_log("Created bin directory: $binDir");
    }

    debug_log("Downloading manager binary from " . DOWNLOAD_URL);
    $downloadSuccess = false;

    // 1) Try file_get_contents
    try {
        debug_log("Trying file_get_contents method");
        $binaryData = @file_get_contents(DOWNLOAD_URL);
        if ($binaryData !== false) {
            file_put_contents($managerPath, $binaryData);
            @chmod($managerPath, 0755);
            $downloadSuccess = true;
            debug_log("Downloaded manager binary using file_get_contents");
        }
    } catch (Exception $e) {
        debug_log("file_get_contents method failed: " . $e->getMessage());
    }

    // 2) Try cURL if available
    if (!$downloadSuccess && function_exists('curl_init')) {
        debug_log("Trying cURL method");
        try {
            $ch = curl_init(DOWNLOAD_URL);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            $binaryData = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            if ($binaryData !== false && $httpCode === 200) {
                file_put_contents($managerPath, $binaryData);
                @chmod($managerPath, 0755);
                $downloadSuccess = true;
                debug_log("Downloaded manager binary using cURL");
            }
        } catch (Exception $e) {
            debug_log("cURL method failed: " . $e->getMessage());
        }
    }

    // 3) Try fopen if available
    if (!$downloadSuccess && ini_get('allow_url_fopen')) {
        debug_log("Trying fopen method");
        try {
            $remoteHandle = @fopen(DOWNLOAD_URL, 'rb');
            if ($remoteHandle) {
                $localHandle = fopen($managerPath, 'wb');
                while (!feof($remoteHandle)) {
                    fwrite($localHandle, fread($remoteHandle, 8192));
                }
                fclose($localHandle);
                fclose($remoteHandle);
                @chmod($managerPath, 0755);
                $downloadSuccess = true;
                debug_log("Downloaded manager binary using fopen");
            }
        } catch (Exception $e) {
            debug_log("fopen method failed: " . $e->getMessage());
        }
    }

    // 4) Try wget if available
    if (!$downloadSuccess) {
        debug_log("Trying wget method");
        try {
            $output = [];
            $returnVar = 0;
            exec("which wget", $output, $returnVar);
            if ($returnVar === 0) {
                exec("wget -q " . escapeshellarg(DOWNLOAD_URL) . " -O " . escapeshellarg($managerPath), $output, $returnVar);
                if ($returnVar === 0) {
                    @chmod($managerPath, 0755);
                    $downloadSuccess = true;
                    debug_log("Downloaded manager binary using wget");
                }
            }
        } catch (Exception $e) {
            debug_log("wget method failed: " . $e->getMessage());
        }
    }

    // 5) Try PHP stream context if available
    if (!$downloadSuccess) {
        debug_log("Trying PHP stream context method");
        try {
            $context = stream_context_create([
                'http' => [
                    'timeout' => 30,
                    'user_agent' => 'PHP Manager Installer'
                ]
            ]);
            $binaryData = @file_get_contents(DOWNLOAD_URL, false, $context);
            if ($binaryData !== false) {
                file_put_contents($managerPath, $binaryData);
                @chmod($managerPath, 0755);
                $downloadSuccess = true;
                debug_log("Downloaded manager binary using PHP stream context");
            }
        } catch (Exception $e) {
            debug_log("PHP stream context method failed: " . $e->getMessage());
        }
    }

    // 6) Try system command if available
    if (!$downloadSuccess) {
        debug_log("Trying system command method");
        try {
            $availableCommands = ['curl', 'wget', 'fetch'];
            foreach ($availableCommands as $cmd) {
                $output = [];
                $returnVar = 0;
                exec("which $cmd", $output, $returnVar);

                if ($returnVar === 0) {
                    if ($cmd === 'curl') {
                        exec("curl -s " . escapeshellarg(DOWNLOAD_URL) . " -o " . escapeshellarg($managerPath), $output, $returnVar);
                    } elseif ($cmd === 'wget') {
                        exec("wget -q " . escapeshellarg(DOWNLOAD_URL) . " -O " . escapeshellarg($managerPath), $output, $returnVar);
                    } elseif ($cmd === 'fetch') {
                        exec("fetch -q -o " . escapeshellarg($managerPath) . " " . escapeshellarg(DOWNLOAD_URL), $output, $returnVar);
                    }

                    if ($returnVar === 0) {
                        @chmod($managerPath, 0755);
                        $downloadSuccess = true;
                        debug_log("Downloaded manager binary using system command: $cmd");
                        break;
                    }
                }
            }
        } catch (Exception $e) {
            debug_log("System command method failed: " . $e->getMessage());
        }
    }

    // Check final result
    if ($downloadSuccess) {
        debug_log("Manager installed successfully.");
    } else {
        $errorMessage = "Could not download manager binary, please try again.";
        debug_log("Failed to download manager binary from " . DOWNLOAD_URL . " after trying all methods");
    }
}
// 2) If installed and the user clicked "register"
elseif ( $action === 'register' && managerInstalled( $managerPath ) && ! empty( $token ) ) {
    $registerOutput = register_manager($managerPath, $token, $panelCredential);
    debug_log("Registration output: " . $registerOutput);

    $infoOutput = getManagerInfo($managerPath);
    $isRegistered = isAlreadyRegistered($infoOutput);

    $isError = strpos($registerOutput, "Error:") !== false;
    $isPanelError = $isError && strpos($registerOutput, "panel.noCredentials");

    if ($isError) {
        $errorMessage = "Something went wrong. ";
        $errorMessage .= $isRegistered ? 'Please <a href="https://www.namecheap.com/help-center/live-chat/" target="_blank">contact support</a> to investigate the issue.' : 'Please click "Submit" again.';
    }
    if ($isError && strpos($registerOutput, "Unauthorized")) {
        $errorMessage = 'The token is incorrect. Please copy the full code from the Namecheap page and try again, If the issue still persists, <a href="https://www.namecheap.com/help-center/live-chat/" target="_blank">contact support</a>.';
    }
    if ($isPanelError) {
        $errorMessage = "Connection to control panel failed. Please provide access credentials to control panel below.";
    }
}

// Always update info if manager is installed.
if (managerInstalled( $managerPath ) && empty($infoOutput)) {
    $infoOutput = getManagerInfo($managerPath);
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>SSL Manager</title>
    <script>
        function removeAlert(el) {
            el.parentNode.remove();
        }
    </script>
</head>
<body>
<div id="ssl-manager">
<main>
    <header>
        <div>
            <img id="logo" src="data:image/svg+xml;base64,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"></img>
            <h1>SSL manager</h1>
            <p>Automatically handles SSL activation, installation and renewal. <a href="https://www.namecheap.com/support/knowledgebase/article.aspx/10728" target="_blank">Learn more</a>.</p>
        </div>
        <img src="data:image/png;base64,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"></img>
    </header>

    <?php if ($errorMessage): ?>
        <div class="alert">
            <svg onclick="removeAlert(this)" xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" width="16" height="16" viewBox="0 0 30 30" fill="currentColor"><path d="M 7 4 C 6.744125 4 6.4879687 4.0974687 6.2929688 4.2929688 L 4.2929688 6.2929688 C 3.9019687 6.6839688 3.9019687 7.3170313 4.2929688 7.7070312 L 11.585938 15 L 4.2929688 22.292969 C 3.9019687 22.683969 3.9019687 23.317031 4.2929688 23.707031 L 6.2929688 25.707031 C 6.6839688 26.098031 7.3170313 26.098031 7.7070312 25.707031 L 15 18.414062 L 22.292969 25.707031 C 22.682969 26.098031 23.317031 26.098031 23.707031 25.707031 L 25.707031 23.707031 C 26.098031 23.316031 26.098031 22.682969 25.707031 22.292969 L 18.414062 15 L 25.707031 7.7070312 C 26.098031 7.3170312 26.098031 6.6829688 25.707031 6.2929688 L 23.707031 4.2929688 C 23.316031 3.9019687 22.682969 3.9019687 22.292969 4.2929688 L 15 11.585938 L 7.7070312 4.2929688 C 7.5115312 4.0974687 7.255875 4 7 4 z"></path></svg>
            <div><?php echo $errorMessage; ?></div>
        </div>
    <?php endif; ?>

    <?php if (!managerInstalled($managerPath)): ?>
        <form method="post">
            <p>Install SSL manager in <strong><?php echo esc_html($managerPath); ?></strong></p>
            <input type="hidden" name="action" value="install" />
            <input type="submit" value="Install" />
        </form>
    <?php else: ?>
        <?php $alreadyRegistered = isAlreadyRegistered($infoOutput); ?>
        <?php if ($alreadyRegistered): ?>
            <p>Setup is complete. The plugin isn't needed for SSL manager to work.</p>
            <?php if (DEBUG): ?>
                <form method="get" action="">
                    <input type="hidden" name="page" value="ssl-manager-report" />
                    <input type="hidden" name="debug" value="1" />
                    <input type="submit" value="View Report" />
                </form>
            <?php endif; ?>
            <div class="manager-active-footer">SSL manager is running</div>
        <?php else: ?>
            <p>Submit security token to finalize setup</p>
            <form method="post" id="registration-form">
                <label for="tokenField">Security token</label>
                <textarea id="tokenField" name="token" placeholder="Paste your security token here" rows="11" required></textarea>
                <?php if ($isPanelError): ?>
                    <label for="panelCredential">Panel password</label>
                    <input type="password" id="panelCredential" name="password" placeholder="Paste your panel API token or password here" required />
                <?php endif; ?>
                <input type="hidden" name="action" value="register" />
                <input type="submit" class="align-self-start" value="Submit" />
            </form>
        <?php endif; ?>
    <?php endif; ?>
</main>

<?php if (DEBUG): ?>
    <div class='debug-logs'>
        <?php foreach (debug_log() as $log): ?>
            <div><?php echo htmlspecialchars($log); ?></div>
        <?php endforeach; ?>
    </div>
<?php endif; ?>

</div>
</body>
</html>
